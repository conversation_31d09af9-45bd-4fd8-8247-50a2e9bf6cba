part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthDone extends AuthState {
  const AuthDone({
    required this.loginPAVirtualVO,
  });
  final List<LoginPAVirtualVO> loginPAVirtualVO;
}

class AuthAnswerVerified extends AuthState {
  final String redirectTo;
  final String cardLogged;

  @override
  List<Object> get props => [redirectTo];

  const AuthAnswerVerified( {required this.redirectTo, required this.cardLogged,});
}

class AuthError extends AuthState {
  const AuthError({required this.message});
  final String message;
  @override
  List<Object> get props => [message];
}
