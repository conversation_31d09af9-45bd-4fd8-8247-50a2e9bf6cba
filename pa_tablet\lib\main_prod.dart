import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pa_tablet/screen/main.dart';
import 'package:pa_tablet/shared/flavor_config.dart';
import 'package:pa_tablet/shared/locator.dart';

void main() async {
  FlavorConfig(flavor: Flavor.production, values: FlavorPROD());

  Locator.setup();
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp();

  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  runApp(const PaTablet());
}
