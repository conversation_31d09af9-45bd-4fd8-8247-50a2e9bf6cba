// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:pavirtual_tablet/model/room_model.dart';
// import 'package:pavirtual_tablet/shared/widget/twilio/conference_page.dart';
// import 'package:twilio_programmable_video/twilio_programmable_video.dart';

// class TwilioService {
//   late Room _room;
//   final Completer<Room> _completer = Completer<Room>();

//   void _onConnected(Room room) {
//     debugPrint('Connected to ${room.name}');
//     _completer.complete(_room);
//   }

//   void _onConnectFailure(RoomConnectFailureEvent event) {
//     debugPrint(
//         'Failed to connect to room ${event.room.name} with exception: ${event.exception}');
//     _completer.completeError(event.exception!);
//   }

//   Future<Room> connectToRoom() async {
//     // Retrieve the camera source of your choosing
//     var cameraSources = await CameraSource.getSources();
//     var cameraCapturer = CameraCapturer(
//       cameraSources.firstWhere((source) => source.isFrontFacing),
//     );

//     var connectOptions = ConnectOptions(
//       accessToken,
//       roomName: "roomName", // Optional name for the room
//       //region: region, // Optional region.
//       preferredAudioCodecs: [
//         OpusCodec()
//       ], // Optional list of preferred AudioCodecs
//       preferredVideoCodecs: [
//         H264Codec()
//       ], // Optional list of preferred VideoCodecs.
//       //audioTracks: [LocalAudioTrack(true)], // Optional list of audio tracks.
//       // dataTracks: [
//       //   LocalDataTrack(
//       //     DataTrackOptions(
//       //         ordered:
//       //             ordered, // Optional, Ordered transmission of messages. Default is `true`.
//       //         maxPacketLifeTime:
//       //             maxPacketLifeTime, // Optional, Maximum retransmit time in milliseconds. Default is [DataTrackOptions.defaultMaxPacketLifeTime]
//       //         maxRetransmits:
//       //             maxRetransmits, // Optional, Maximum number of retransmitted messages. Default is [DataTrackOptions.defaultMaxRetransmits]
//       //         name: name // Optional
//       //         ), // Optional
//       //   ),
//       // ], // Optional list of data tracks
//       videoTracks: [
//         LocalVideoTrack(true, cameraCapturer)
//       ], // Optional list of video tracks.
//     );
//     _room = await TwilioProgrammableVideo.connect(connectOptions);
//     _room.onConnected.listen(_onConnected);
//     _room.onConnectFailure.listen(_onConnectFailure);
//     return _completer.future;
//   }

//   testRoom(BuildContext context) {
//     //connectToRoom();
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => ConferencePage(
//           roomModel: RoomModel(
//               name: "ugabuga123",
//               token: accessToken,
//               identity: "Teste",
//               type: TwilioRoomType.peerToPeer),
//         ),
//       ),
//     );
//   }
// }

// const accessToken =
//     "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiIsImN0eSI6InR3aWxpby1mcGE7dj0xIn0.eyJqdGkiOiJTSzg5MDdlZjAwMmY0NGEzN2M4OGQ3ZDU1NDkzNGZkNjhmLTE2NjU1MTM5NzAiLCJpc3MiOiJTSzg5MDdlZjAwMmY0NGEzN2M4OGQ3ZDU1NDkzNGZkNjhmIiwic3ViIjoiQUNlMDkzZTk4NGQxNDRmNTcwNTk1MDhlMDAxZDQ2N2ZiZiIsImV4cCI6MTY2NTUxNzU3MCwiZ3JhbnRzIjp7ImlkZW50aXR5IjoicGEiLCJ2aWRlbyI6e319fQ.0aAmQ2XKPaP06e2P1g_N-HOV08Q80xD_xeqJ_8VCMLk";
