// ignore_for_file: non_constant_identifier_names
// OBS1: Usar as mensagens SOMENTE NOS SERVICES
// OBS2: Blocs não tem que ter mensagens, elas virão do service

abstract class MessageException {
  static const general = 'Sistema indisponível no momento. Tente mais tarde.';
  static const genericNoData = "Não existe dados disponiveis no momento";
  static const genericTimeout =
      "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde. (TIMEOUT)";

  //Beneficiary
  static const beneficiaryData = 'Falha ao recuperar dados do beneficiário';

  static const generalAttendance =
      "Não foi possível criar o atendimento no momento, tente novamente mais tarde.";
}
