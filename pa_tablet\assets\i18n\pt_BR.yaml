common:
  locale: pt_BR
  cancel: Cancelar
  register: Cadastrar
  continue: Continuar
  confirm: Confirmar
  update: Atualizar
  close: <PERSON><PERSON><PERSON>
  exit: Sair
  search: Pesquisar
  medicalGuide: Guia Médico
  dateFormat: dd/MM/yyyy
  timeFormat: HH:mm
  dateTimeFormat: dd/MM/yyyy - HH:mm
  months:
    january: Janeiro
    february: Fevereiro
    march: Março
    april: Abril
    may: Maio
    june: Junho
    july: Julho
    august: Agosto
    september: Setembro
    october: Outubro
    november: Novembro
    december: Dezembro
  fieldsName:
    name: Nome
    beneficiaryName: Nome do Beneficiário
    motherName: Nome da mãe
    fatherName: Nome do pai
    spouseName: Nome do(a) conjuge
    birthDate: Data de nascimento
    maritalStatus: Estado civil
    cpf: CPF
    rg: RG
    email: E-mail
    phone: Telefone
    phone1: Telefone 1
    phone2: Telefone 2
    address: Endereço
    protocol: Protocolo
    procedure: Procedimento
    date: Data
    location: Localização
    status: Status
    provider: Prestador
    professional: Profissional
    schedule: Horário
    scheduleType: Tipo de consulta
    specialty: Especialidade
    schooling: Escolaridade
    profession: Profissão
    religion: Religião
    nationality: Naturalidade
    emergencyContact: Contato de emergência
    card: Carteira
    service: Serviço
  fieldsValidatorsWarnings:
    fieldRequired: Campo obrigatório
    fieldMinCaracters: Campo deve ter no mínimo 3 caracteres
    invalidPhone: Telefone inválido
    invalidCel: Celular inválido
    invalidEmail: E-mail inválido
    invalidRg: RG inválido
  verifyData:
    header:
      span1: "Verifique se seus "
      span2: "dados cadastrais "
      span3: "estão corretos!"
  checkBoxText:
    checkWhatsapp:
      span1: "Autorizo "
      span2: "que a Unimed Fortaleza "
      span3: "me envie "
      span4: "notificações via "
      span5: "Whatsapp."
    checkEmail:
      span1: "Solicito "
      span2: "recebimento do "
      span3: "termo "
      span4: "por "
      span5: "e-mail. "
login:
  doc: CPF
  password: Senha
  validation:
    empty: Digite a senha
    lessThan: A senha deve ter entre 6 a 10 caracteres
  forgot: Esqueci minha senha
  new: Novo Cadastro
  signin: Acessar
logout:
  title: Tem certeza?
  text: Quer mesmo sair do aplicativo?
  confirm: Sim, quero sair

home:
  main:
    version:
      alert: Alerta de Versão
      text: "Nova versão disponível: {remoteVersion} {br}Sua Versão atual: {localVersion}"
      update: Atualizar
  news:
    title: Notícias
    like: Você curtiu isso!
    loading: Carregando Notícias ...
    noPageBack: Nenhuma Página atrás no histórico
    noPageNext: Nenhuma Página a frente no histórico
  buttons:
    financial: Financeiro
    medicalAppointment: Consultas
    schedulingAppointment: Agende a sua consulta
    requests: Autorizações
    myPlan: Meu Plano
    virtualCard: Cartão Virtual
    optional: Opcionais
    evaluate: Avaliar
    exams: Exames
    healthConection: Consultas Virtuais
    checkinEmergency: Check-in Emergência
    checkinUnity: Check-in Unidade
    registerBiometrics: Cadastro de Biometria
  profile:
    choose: Escolha o perfil desejado
  loadingProfile:
    text: Carregando perfil ...
  disableAccount:
    alert:
      title: Desativando conta do aplicativo
      text: Foi enviado um código de confirmação para o seu email, ${email}, favor inserir no campo correspondente
      sendingToken: Enviando token de confirmação
      confirmationCode: Código de confirmação do e-mail
      buttons:
        resend: REENVIAR CÓDIGO
        back: VOLTAR
        action: CONFIRMAR

unimedConecta:
  title: Consultas Virtuais
  options:
    pa:
      title: Pronto Atendimento
      description: Pronto Atendimento virtual
      content:
        message: Escolha a forma de atendimento
        urgency: Pronto Atendimento
        return: Retorno
      alert:
        span1: Você está iniciando o Pronto Atendimento Virtual.
        span2: Deseja continuar? 
    paChild:
      title: Pronto Atendimento Pediátrico
      description: Pronto Atendimento virtual
      content:
        message: Escolha a forma de atendimento
        urgency: Pronto Atendimento Pediátrico
        return: Retorno
    clinics:
      title: Teleconsulta Agendada
      description: Agendamento de teleconsulta
      content:
        message: Escolha a especialidade de atendimento
        specializedDoctors: Médicos especialistas
        healthAttention: Saúde Integral
    teleconsultation:
      title: Teleconsulta Eletiva
      description: Teleconsultas
    phone:
      title: Unimed Fone
      description: Atendimento por telefone
      content:
        notSigned: Contrate agora o Serviço Unimed Fone
        signed: Serviço já está contratado!
        toSign: Contratar serviço
        access: Acessar serviço
    urgency:
      title: Unimed Urgente
      description: Serviço de ambulância
    historyConsultation:
      title: Histórico de Consultas
      description: Histórico e documentos
      content:
        message: Acesse o histórico e documentos de suas consultas virtuais
        button: Histórico e documentos

checkinUnity:
  title: Check-in Unidade
  options:
    emergency:
      title: Emergência 2.0
      description: Adiante sua chegada a emergência
      content:
        message: Escolha a forma de atendimento
    cirurgic:
      title: Cirurgia
      description: Adiante os procedimentos pré-cirúrgicos
      content:
        message: Escolha a forma de atendimento
    laboratory:
      title: Laboratório
      description: Adiante sua chegada e guias de exames
      content:
        message: Escolha a forma de atendimento

fullHealthAttention:
  appBarTitle: Saúde Integral
  favoriteRemoved: removido dos favoritos
  specialties: Especialidade
  loadingSchedule: Carregando o mês de {month}
  scheduleConsultation: Marcar Consulta
  creatingConsultation: Registrando agendamento...
  time: Horário
  toCall: Ligar
  toSchedule: Agendar
  teleconsultation: Teleconsulta
  loadingProvidersByDay: Carregando médicos para o dia
  availableProvidersByDay: Médicos disponíveis para o dia
  lookProvidersByDate: Selecione uma data
  confirmed:
    scheduled: Agendado
    protocol: Protocolo
    beneficiary: Beneficiário
    card: Carteira
    specialty: Especialidade
    provider: Prestador
    consultation: Teleconsulta
    return: Voltar
    date: Data
    time: Hora
  errors:
    noAvailableDay: Dia {day} não possui horários disponíveis
    wrong_bloc: Erro no agendamento
    noTime: Sem agenda disponível
    createConsultation: Não foi possível concluir o agendamento
    emptySpecialty: Especialidade não pode estar vazio
    emptyTime: Hora não pode estar vazia
    validateTitle: Preencha todos os dados
    getSchedule: Não foi possível carregar agenda
    unableToLoad: Não possível concluir esta operação
    emptyList: Lista Vazia
    unableToGetProviders: Não foi possível listar prestadores.
exams:
  appBarTitle: Exames
  itens:
    scheduleImage: Agendar exames de imagem
    scheduleLab: Agendar exames laboratorias
    history: Histórico de exames agendados
    results: Resultado de exames
  scheduleExams:
    title: Agendar exame de imagem
    exit:
      title: Tem certeza?
      text: Quer mesmo sair do Agendamento de Exames?
      confirm: Sim, quero sair
    steps:
      myData: Meus Dados
      guides: Guias
      exams: Exames
      quiz: Questionário
      schedule: Agenda
      biometric: Biometria
      informations: Informações
    data:
      title: Confirmar agendamento
    questionary:
      title:
        span1: Responda as
        span2: perguntas
        span3: para que possamos identificar as características do seu exame
      imc:
        title:
          span1: Para continuar precisamos que você
          span2: informe seus dados
      comorbidities:
        title:
          span1: Você possui alguma comorbidade?
          span2: Selecione sua(s) comorbidade(s)
      alertSedacao: 
        title: Aviso
        text: No momento, não é possível agendar o exame com sedação selecionado pelo aplicativo. Pedimos, por gentileza, que entre em contato com a Central de Agendamentos de Exames no número (85) 3277.6100 para agendá-lo. Informamos que estamos trabalhando para melhor atendê-lo (a).
        button: Ligar
    exams: 
      header:
        span1: "Selecione os"
        span2: " exames "
        span3: " que deseja realizar no "
        span4: "Setor de Imagem."
      body:
        text1: "(*) Os serviços foram solicitados eletronicamente, porém estão sujeitos ás regras de autorização no momento da realização."
        text2: "Selecionar exames:"

  tabs:
    laboratory: Laboratoriais
    images: Imagens
  loadingExams: Carregando exames...
  forcedLoadingExams: Por questões de segurança, precisamos atualizar a lista de exames...
  errorGetExams: Erro ao trazer os exames!
  orderResults: Resultados do pedido
  downloadAll: Baixar Todos Disponíveis
  examsType: Tipo de exame
  material: Material
  doctorName: Nome do médico
  exam: Exame
  creationDate: Data de criação
  realizationDate: Realizado em
  solicitationDate: Solicitado em
  scheduledDate: Previsão do resultado
  openReport: LAUDO
  openImage: IMAGEM
  openImages: IMAGENS
  report: Laudo
  image: Imagem
  available: DISPONÍVEL
  unavailable: INDISPONÍVEL
  order: Pedido
  status:
    available: Liberado
    inComing: Em Andamento
    pending: Pendente
  alert: Os exames só estarão disponíveis quando o {nomeBeneficiario} entrar com seu próprio usuário no aplicativo.
  
authToken:
  loadingToken: Aguardando a solicitação de token do balcão de atendimento...
  errorToken: Não foi possível acessar o Token de Autorização!
  successGenerateTitle: seu token foi gerado com sucesso!
  showToken: Apresente este token no atendimento
  generateNewToke: Gerar novo Token
  expiresIn: Empira em

profileData:
  appBarTitle: Meus Dados
  loadingProfileData: Carregando os dados do perfil...
  defaultError: Não foi possível acessar os dados do perfil!
  editScreen:
    appBarTitle: Edição de endereço
    zipcode: CEP
    streetName: Logradouro
    number: Número
    complement: Complemento
    success: Endereço atualizado com sucesso!
    error: Não foi possível atualizar!
  pickAddress:
    typeCep: Digite seu cep
    errorZipcode: Não foi possível buscar o endereço
  updateEmail:
    title: Atualização de email
    message: Deseja atualizar o seu e-mail para
  updatePhone:
    title: Atualização de telefone
    message: Deseja atualizar o seu telefone para
  alert:
    title: Confirmação de Dados
    text: Você tem certeza que os dados atualizados estão corretos?
    updateTitle: Atualização dos Termos e Dados
    updateText: Foi enviado um código de confirmação para a forma de contato atualizada, favor inserir no campo correspondente
    confirmationCodeField: Código de Confirmação
    success: Atualização feita com sucesso
    buttons:
      action: CONFIRMAR
      back: VOLTAR
      resend: REENVIAR CÓDIGO
  beneficiary:
    name: Nome do Beneficiário
    mothersName: Nome da Mãe
    gender: Sexo
    birthday: Data de Nascimento
    id: RG
    idComplete: Documento de identidade
    doc: CPF
    cns: CNS
    cnsComplete: Carteira Nacional de Saúde
    code: Código do Beneficiário
    planCard: Cartão do plano
    address: Endereço
    email: E-mail
    phone: Telefone
    planName: Nome do plano
    planRegister: Registro do plçano
    contractNumber: Nº do Contrato
    contractType: Tipo de contratação
    regulation: Regulamentação
    accommodation: Acomodação
    coverage: Abrangência
    contractDate: Data do Contrato
    coverageInit: Início da Cobertura

medicalGuide:
  main:
    lookingFor: Buscando por {item}
    advancedSearch: Busca avançada
    resultAdvancedSearch: Resultado da busca avançada
    resultNormal: Resultados para {item}
    favorites: Favoritos
    lookingForFavorite: Buscando favoritos
    favoritesResult: Resultados favoritos
    search: Pesquisar...
    error:
      favorites: Falha ao buscar favoritos
      others: Falha ao buscar por {item}
      providerNoAddress: "O Prestador ${name} não tem endereços cadastrados."
  header:
    initialMessage: Olá, seja bem-vindo!
    hello: "Olá {name}!"
    advancedSearch: Busca avançada
    profiles: Mudar perfil
    logout: Sair
    inputHint: Pesquise por médico, especialidade, clínica
    emptySearch: Digite o que deseja procurar
    version: Versão
    privacyPolicy: Política de privacidade
    useTerms: Termos de Uso
    authToken: Token de Autorização
    myData: Meus dados
    disableAccount: Desativar conta
  advancedSearch:
    providerType: Tipo de prestador
    provider: Prestador
    specialty: Especialidade
    city: Cidade
    neighborhood: Bairro
    clear: Limpar filtros
    noProviders: Não existem prestadores cadastrados com este tipo e especialidade no momento
    validators:
      providerType: Você deve selecionar um tipo de prestador
      specialty: Você deve selecionar uma especialidade

veService:
  consultationList:
    consultDocs: Documentos da consulta
    doc: Documento
    docs: Documentos
    noDocs: Sem Documentos
    noVEAvailable: Sem Pronto Atendimentos disponíveis.
    errorListVE: Não foi possível Listar os Pronto Atendimentos
    loadingVE: Carregando pronto atendimentos...
    protocol: Protocolo
    emergencyService: Pronto Atendimento
    attendanceDate: Data de Atendimento
    cancelationDate: Data de Cancelamento
    status: Status
    provider: Prestador
    doctor: Médico
    crm: CRM
    authorizationDate: Data de autorização
    veStartedTimeDate: Início da Jornada do PA
    doctorEnteredRoomDateTime: Médico entrou na sala as
    beneficiaryEnteredRoomDateTime: Beneficiário entrou na sala as
    doctorCloseDateTime: Médico encerrou às
  teleconsultation:
    messageSendEmailTerms: Enviando e-mail com termos
    messageSaveWhatsappNumber: Salvando dados do Whatsapp
    messageAuthorize: Autorizando atendimento
    messageCreateAttendance: Gerando atendimento
    messageQueuePosition: Recuperando sua posição da fila
    messageGetTeleconsultationRoomInfo: Recuperando dados da sala de atendimento virtual
  common:
    client: Beneficiário
    card: Carteira
    motherName: Nome da mãe
    birthDate: Data de nascimento
  error:
    queue: Algo deu errado com o atendimento.
    invalidQueuePos: Posição na fila inválida!
    actionButton: Tentar novamente
    queuePosition: Estamos tentando processar seu lugar na fila. Aguarde.
    statusRoom: Não foi possível consultar as informações do atendimento
  main:
    title: PA Virtual
    footerButton:
      painsAndSymptoms1: Autorização
      painsAndSymptoms2: Selecionar Dores
      queue1: Entrar na sala
      queue2: Por favor aguarde...
      queueException: Tentar novamente
      terms: Meus dados
      myData: Outros Dados
      vaccine: Comorbidades
      comorbidities: Sintomas
      imc: Vacinas
    steps:
      myData: Meus Dados
      terms: Termos
      painsAndSymptoms: Sintomas
      comorbidities: Comorbidades
      queue: Consultório
      imc: Outros Dados
      vaccine: Vacina
  exit:
    title: Tem certeza?
    text: Quer mesmo sair do PA Virtual?
    confirm: Sim, quero sair
  bioId:
    camPermission:
      title: Acesso a câmera
      text: Precisamos acessar sua câmera para validar a biometria facial
      authorize: Autorizar
  info:
    loading:
      span1: Estamos iniciando o Pronto Atendimento Virtual para
      span2: com carteira
    warning:
      title: ATENÇÃO
      span1: "Caso tenha "
      span2: "dificuldade para respirar, dor severa "
      span3: "repentina ou sangramento intenso sem controle, "
      span4: "saiba que você precisa procurar um Pronto Socorro mais próximo.  "
    whatsVEService:
      title: O que é Pronto Atendimento Virtual?
      span1: "O "
      span2: "PRONTO ATENDIMENTO VIRTUAL "
      span3: "consiste em uma "
      span4: "chamada de vídeo, "
      span5: "realizada "
      span6: "entre médico e paciente, "
      span7: "sem exame presencial, com a possibilidade de prescrição de tratamento, solicitação de exames, emissão de atestado, declaração de atendimento e outros procedimentos, validados com assinatura digital. "
      span8: "Caso o "
      span9: "profissional de saúde, "
      span10: "perceba a necessidade de "
      span11: "avaliação presencial "
      span12: "o paciente "
      span13: "será orientado "
      span14: "de como deverá proceder, dentro da necessidade"
  myData:
    header:
      span1: "Para continuar precisamos que "
      span2: "você "
      span3: "confirme "
      span4: "seus dados."
    inputPhone: Telefone
    inputCellphone: Celular
    inputEmail: E-mail
    inputRG: Documento RG
    confirmData: Confirmar Dados
    checkWhatsapp:
      span1: "Autorizo "
      span2: "que a Unimed Fortaleza "
      span3: "me envie "
      span4: "notificações via "
      span5: "Whatsapp."
    checkEmail:
      span1: "Solicito "
      span2: "recebimento do "
      span3: "termo "
      span4: "por "
      span5: "e-mail. "
    inputWhatsApp: Telefone para whatsapp
    inputEmailTerm: E-mail para envio do termo
    validators:
      fieldRequired: Campo obrigatório
      fieldMinCaracters: Campo deve ter no mínimo 3 caracteres
      invalidPhone: Telefone inválido
      invalidCel: Celular inválido
      invalidEmail: E-mail inválido
      invalidRg: RG inválido
  queue:
    release: Paciente com alta médica.
    doctorIsNotInRoom: O Médico ainda não está na sala. Aguarde um momento.
    ready:
      part1: Pronto!
      part2: Você já pode entrar na sala.
    waitDoctor:
      part1: Aguarde alguns instantes,
      part2: o médico entrará na sala em breve!
    wait:
      part1: Aguarde, seu atendimento
      part2: iniciará em breve.
    position: Posição na fila
    serviceNo: Número do atendimento
    protocol: Protocolo do Atendimento
    serviceHours: Horário do Atendimento
    bioId: Biometria Facial
    valid: VALIDADA
    waitingTime: Tempo médio de espera
  symptomsPains:
    header:
      wait: Aguarde um instante, estamos processando seu atendimento ...
      span1: "O que "
      span2: "você "
      span3: "está "
      span4: sentindo?
      span5: Selecione
      span6: " o máximo possível de "
      pains: dores.
      symptoms: sintomas.
    alert:
      title: Selecione alguma comorbidade, sintoma ou dor
      body: Você deve selecionar pelo menos uma comorbidade ou um sintomas ou uma dor para continuar
  comorbidities:
    header:
      span1: Você
      span2: possui alguma
      span3: comorbidade?
  terms:
    pdf: Termos de Consentimento
    loadingTerms: Carregando termo de aceitação...
    header:
      span1: "Para continuar você "
      span2: "deve confirmar "
      span3: "o seguinte:"
    footerForm: "* Poderá haver cobrança proporcional ao gozo dos serviços colocados à disposição do consumidor de acordo com a data de vencimento do seu plano de saúde."
    error: Erro ao carregar termos.
    tryAgain: tentar novamente
    agree: "Eu li e aceito os "
    checkboxTerms: termos e condições
    plus18: Eu tenho ou sou maior de 18 anos
  vaccine:
    header:
      span1: Para continuar precisamos que você
      span2: informe os dados da vacina.
    mark: Informe a marca da vacina
    addButton: ADICIONAR DOSE
    lastDate: Data da última dose
    noVaccineLabel: Declaro que não tomei nenhuma dose da vacina contra COVID-19
    deleteVaccine:
      text: Quer mesmo remover
      title: Tem certeza?
      confirm: Sim, quero remover
  featureDiscovery:
    myData:
      numericStep:
        part1: Etapas concluídas
        part2: do atendimento
      iconStep:
        part1: Etapa atual
        part2: do atendimento.
      info:
        part1: Confira seus dados.
      actionButton:
        part1: Após preencher cada
        part2: etapa do atendimento,
        part3: este botão te levará
        part4: para a etapa seguinte.
      phone:
        part1: Informe seu número
        part2: de telefone atual,
        part3: celular ou fixo!
      whatsapp:
        part1: Se preferir, nos autorize
        part2: a te enviar notificações
        part3: via whatsapp!
      email:
        part1: Informe seu melhor
        part2: endereço de e-mail.
      emailTerm:
        part1: Você pode ainda,
        part2: solicitar o termo
        part3: de aceite por e-mail!
    imc:
      imcBoxs: Preencha os dados de peso e altura para cálculo de IMC
      imcBoxsChild: Preencha os dados de peso
      medicineCheckBox: Em seguida marque se é alergico(a) ou faz uso continuo de algum medicamento
    vaccine:
      selectVaccine: Preencha o campos informando a vacina
      noVaccine: Caso não tenha tomado nenhuma vacina seleciona a checkbox e depois clique em continuar
    queue:
      position:
        part1: Aqui é sua posição na
        part2: fila de espera
        part3: para o atendimento virtual.
      protocol:
        part1: Esse é seu
        part2: número de atendimento!
      actionButton:
        inQueue:
          part1: Quando chegar sua vez
          part2: o botão será ativado
          part3: e você poderá entrar na sala.
        enter:
          part1: Aqui você entra na
          part2: sala de atendimento virtual.
    symptomsPains:
      symptoms: os sintomas
      pains: as dores
      message:
        part1: "Selecione {value}"
        part2: que apresenta.
      actionButton:
        symptomsDone:
          part1: Realize reconhecimento facial
          part2: para autorizar seu atendimento.
          part3: Caso não tenha um cadastro,
          part4: o mesmo será realizado...
        symptoms:
          part1: Após selecionar os sintomas
          part2: ir para etapa seguinte.
    comorbidities:
      message:
        part1: "Selecione as comorbidades"
        part2: que apresenta.
      actionButton:
        comorbiditiesDone:
          part1: Após preencher cada
          part2: etapa do atendimento,
          part3: este botão te levará
          part4: para a etapa seguinte.
        comorbidities:
          part1: Após selecionar as comorbidades
          part2: ir para etapa seguinte.
    terms:
      read:
        part1: Leia os termos
        part2: do serviço.
        part3: e, se de acordo, aceite-os.
      plus18:
        part1: Informe ter ou ser
        part2: maior de 18 anos.
      actionButton:
        part1: Ir para etapa de
        part2: checagem de dados
    gotIt: Ok, Entendi. Obrigado!

evaluation:
  endTeleconsutation: Seu atendimento foi encerrado
  title: Em geral, qual o seu grau de satisfação com o APP Cliente Unimed Fortaleza?
  subtitle: Como podemos fazer para melhorar?
  evaluationScale: Em uma escala de 0 a 10, você indicaria esse serviço?
  evaluate: Avalie nosso serviço
  successEvaluate: Sua avaliação foi enviada com sucesso! Obrigado por nos ajudar a melhorar
  buttons:
    sendFeedback: AVALIAR
    notSendFeedback: AGORA NÃO
    closeSucess: OK
  alertCloseEvaluation: Tem certeza que deseja fechar a avaliação?
  exit:
    title: Deseja sair da avaliação?
    text: Sua avaliação demora apenas alguns segundos e é muito importante para melhorarmos a sua experiência no aplicativo.
    confirm: AVALIAR
    notConfirm: SAIR

financial:
  readjustRules:
    appBarTitle: Termo de reajuste
    title: Atenção
    message1: A Unimed Fortaleza suspendeu, entre os meses de setembro a dezembro de 2020, os reajustes contratuais anuais e por variação de faixa etária dos contratos de planos de saúde individuais e coletivos regulamentados.{br} Conforme divulgado em nosso site, a cobrança da recomposição dos valores suspensos passa a incidir a partir de março de 2021, com parcelamento igual e sucessivo em 12 (doze) mensalidades (padrão definido pela Agência Nacional de Saúde Suplementar).{br} Para negociação do parcelamento citado, criamos para você este ambiente virtual e com toda segurança.
    message2: Se tiver dúvidas sobre a nossa Política de Privacidade e Tratamento de Dados Pessoais,
    access: acesse
    accessLink: https://www.unimedfortaleza.com.br/privacidade
    message3: ou nossos canais de atendimento.{br}
    message4: Ciente das informações acima, deseja continuar?{br}
    yes: Sim
    no: Não
  debitAccount:
    infos:
      accountNumber: Número da Conta
      agency: Agência
      bank: Banco
      codOperation: Código de Operação
      email: Email
    loading:
      accede: Enviando dados da sua adesão
      banks: Carregando bancos...
    buttons:
      accede: "ADERIR DÉBITO EM CONTA"
      remove: "REMOVER DÉBITO EM CONTA"
      accede2: "ADERIR"
    remove:
      title: Tem certeza?
      text: Tem certeza que deseja remover sua adesão ao débito em conta?
      confirm: Sim

checkinEmergency:
  header:
    title: Check-in Emergência
    steps:
      myData: Meus Dados
      imc: IMC
      comorbidities: Comorbidades
      symptoms: Sintomas
      biometric: Biometria
      qrCode: QR Code
  redirectToPA:
    textSpan1: De acordo com suas informações, você pode ser atendindo virtualmente. Deseja inciar?
    textSpan2: Tempo de espera
  exit:
    title: Tem certeza?
    text: Quer mesmo sair do Check-in Emergência?
    confirm: Sim, quero sair
  newAttendance:
    title: Tem certeza?
    text: Quer mesmo criar um novo Check-in Emergência?
    confirm: Sim
  qrCode:
    header:
      span1: QR Code
      span2: gerado com sucesso!
    cancel: VOLTAR
    newAttendance: CRIAR NOVO
    messageError: Não foi possível gerar o QR Code no momento.
    expirationAlert:
      title: Tempo do QR Code expirado
      message: O QR Code gerado não é mais válido. É necessário criar um novo checkin de emergência
  ticketPass:
    headerPass: Sua Senha
    headerHistoric:
      span1: Histórico
      span2: de atendimento
    cancel: SAIR DA FILA
checkinLaboratory:
  header:
    title: Check-in Laboratório
    steps:
      myData: Meus Dados
      guides: Guias
      exams: Exames
      biometric: Biometria
      qrCode: QR Code
  exit:
    title: Tem certeza?
    text: Quer mesmo sair do Check-in Laboratório?
    confirm: Sim, quero sair
  myGuide:
    header:
      span1: "Selecione a"
      span2: " guia "
      span3: "para escolher seus exames!"
  myExam:
    header:
      span1: "Selecione os"
      span2: " exames "
      span3: "que deseja realizar no laboratório"
    body:
      text1: "(*) Os serviços foram solicitados eletronicamente, porém estão sujeitos ás regras de autorização no momento da realização."
      text2: "Selecionar exames:"
  qrCode:
    title: Check-in Laboratório
    subTitle: QR Code
    header:
      span1: QR Code
      span2: gerado com sucesso!
    cancel: FECHAR
    toView: VISUALIZAR
    back: VOLTAR
    buttonNewAttendance: CRIAR NOVO
    messageError: Não foi possível gerar o QR Code no momento.
    expirationAlert:
      title: Tempo do QR Code expirado
      message: O QR Code gerado não é mais válido. É necessário realizar um novo procedimento
  newAttendance:
    title: Tem certeza?
    text: Quer mesmo criar um novo Check-in Laboratório?
    confirm: Sim
  myData:
    header:
      span1: "Verifique se seus "
      span2: "dados cadastrais "
      span3: "estão corretos!"
    inputPhone: Telefone
    inputCellphone: Celular
    emergencyContact: Contato de emergência
    inputEmail: E-mail
    inputRG: Documento RG
    confirmData: Confirmar Dados
    checkWhatsapp:
      span1: "Autorizo "
      span2: "que a Unimed Fortaleza "
      span3: "me envie "
      span4: "notificações via "
      span5: "Whatsapp."
    checkEmail:
      span1: "Solicito "
      span2: "recebimento do "
      span3: "termo "
      span4: "por "
      span5: "e-mail. "
    inputWhatsApp: Telefone para whatsapp
    inputEmailTerm: E-mail para envio do termo
    validators:
      fieldRequired: Campo obrigatório
      fieldMinCaracters: Campo deve ter no mínimo 3 caracteres
      invalidPhone: Telefone inválido
      invalidCel: Celular inválido
      invalidEmail: E-mail inválido
      invalidRg: RG inválido
  featureDiscovery:
    guides:
      textSpan: Selecione uma guia para continuar
      textSpan2: Após selecionar as guias
      textSpan3: ir para etapa seguinte.
    exams:
      textSpan: Selecione pelo menos um exame para continuar
      textSpan2: Após selecionar os exames
      textSpan3: ir para etapa seguinte.
schedulingConsultation:
  title: Agende a sua consulta
  footerButtons:
    back: Voltar
    foward: CONTINUAR
  speciality:
    title: Especialidade
    choseEspeciality: Escolha uma especialidade
    loadingEspecialities: "Carregando Especialidades..."
    noEspecialitiesData: Não existe especilidades disponíveis no momento
    textSpan1: Escolha a
    textSpan2: especialidade
    textSpan3: desejada!
    redirectMedicalGuide:
      span1: Desculpe, mas no momento não estamos com agenda online disponível para essa especialidade.
      span2: Deseja verificar a especialidade no Guia Médico?
      buttons:
        action: IR PARA O GUIA MEDICO
        back: VOLTAR
  provider:
    title: Profissionais listados por ordem de datas e horários livres para a especialidade
    noProviderData: Não existe prestadores disponíveis no momento
    noProviderScheduleData: Não existem agendas disponíveis no momento
    loadingSchedules: Carrengando agendas disponíveis
    moreSchedules: Mais agendas
    localization: Localização
    schedule: Agendas disponíveis
  data:
    title: Confirmar agendamento
    alert: O agendamento foi realizado com sucesso!
    myData: MEUS DADOS
    consultationData: DADOS DA CONSULTA
    dataConfirmation: Confirme os dados de contato abaixo
    evaluation:
      title: Em uma escala de 1 a 7, quão fácil foi para você resolver o seu problema?
      commentLabel: O que motivou você a nos dar essa nota?
    whatsappAuthorization:
      span: Autorizo
      span1: que a Unimed Fortaleza
      span2: me envie
      span3: notificações via
      span4: Whatsapp.
    field:
      name: Nome
      card: Carteira
      speciality: Especialidade
      providerName: Profissional
      date: Data
      schedule: Horário
      type: Tipo de consulta
      location: Localização
      email: E-mail
      protocol: Protocolo
      phone: Telefone
      phone2: Telefone segundário
  steps:
    speciality: Especialidades
    provider: Prestador
    myData: Dados
  exit:
    title: Tem certeza?
    text: Quer mesmo sair do agendamento de consulta?
    confirm: Sim, quero sair
checkinSurgery:
  title: Check-in Pré-Cirúrgico
  exit:
    title: Tem certeza?
    text: Quer mesmo sair do Check-in de Cirurgia?
    confirm: Sim, quero sair
  steps:
    select: Agendamentos
    myData: Meus Dados
    latex: Látex
    imc: IMC
    comorbities: Comorbidades
    documents: Documentos
    status: Status
    terms: Termos
    biometry: Biometria
    qrCode: QR Code
    informations: Informações     
  myData:
    header:
      span1: Verifique se seus
      span2: dados cadastrais
      span3: estão corretos!
    checkboxEmail: Solicito recebimento do termo por e-mail
    checkboxNotification: Autorizo que a Unimed Fortaleza me envie notificações via Whatsapp
    validators:
      fieldRequired: Campo obrigatório
      fieldMinCaracters: Campo deve ter no mínimo 3 caracteres
      invalidPhone: Telefone inválido
      invalidCel: Celular inválido
      invalidEmail: E-mail inválido
      invalidRg: RG inválido
  latex:
    title: ALERGIA AO LÁTEX
    text: 
      span1: Nossos pacientes alergicos ao látex requerem um tratamento diferenciado quanto ao horário agendado para a cirurgia    
      span2: Por favor, responda com atenção a informação abaixo
    labelBox: Você possui alergia ao látex?
    divergence:
      title: OLÁ, {nome}
      text:
        span1: Há divergência de informação quanto a alergia ao látex. Essa divergência deve impactar no agendamento da sua cirurgia.
        span2: Infelizmente, não será possível prosseguir.
        span3: Não se preocupe, nosso time de agendamento cirúrgico entrará em contato com você o quanto antes.
        canAdvance: Você confirma essa divergência na informação de alergia ao látex?
    button: RETORNAR
  select:
    header:
      textSpan: Selecione um dos procedimentos
      textSpan2: e prossiga para a próxima etapa
    edit: EDITAR
    continue: Continuar
    visualize: VISUALIZAR
    info: INFORMAÇÕES
    documents: DOCUMENTOS   
  imc:
    header:
      span1: Verifique se seus
      span2: dados cadastrais
      span3: estão corretos!
  comorbidity:
    header:
      span1: Você possui alguma comorbidade?
      span2: Selecione sua(s) comorbidade(s)
  documents:
    header:
      span1: Anexe os documento
      span2: necessários para cirurgia
      span3: RG, CPF, cartão físico ou virtual, guia com autorização e teste de Covid
    remove:
      title: Tem certeza?
      text: Quer mesmo remover o documento anexado?
      confirm: Sim, quero remover
    edit:
      title: Tem certeza?
      text: Quer mesmo editar o documento anexado?
      confirm: Sim, quero editar

  terms:
    title: Check-in Pré-Cirúrgico
    subtitle: Termo de Aceite
    loading: Carregando Termos
    buttons:
      accept: ACEITAR
      back: VOLTAR
  status:
    fileName: NOME DO ARQUIVO
    reason: MOTIVO
    documentOk: DOCUMENTO APROVADO
    documentDisapproved: DOCUMENTO NÃO APROVADO
    documentWaitingApproved: DOCUMENTO AGUARDANDO ANÁLISE
    allOk: Documentos aprovados
    allDisapproved: Um ou mais documento(s) reprovados
    documentsButtons: DOCUMENTOS INFORMATIVOS
  informations:
    header: Seu Check-in Pré-Cirúrgico foi realizado com sucesso!
    informationalDocuments:
      title: Documentos informativos
      header:
        span1: Baixe os documentos
        span2: para ler depois
      downloadAll: BAIXAR TODOS
fieldsValidatorsWarnings:
  fieldRequired: Campo obrigatório
  fieldMinCaracters: Campo deve ter no mínimo 3 caracteres
  invalidPhone: Telefone inválido
  invalidCel: Celular inválido
  invalidEmail: E-mail inválido
  invalidRg: RG inválido
alert:
  biometria:
    biometricsRegistered: Você já possui biometria cadastrada
    biometricsRegisteredError: Erro ao verificar cadastro da biometria
    acessCamera:
      title: Acesso a Câmera
      text: Precisamos acessar sua câmera para validar biometria facial
      button: AUTORIZAR
    validade:
      title: Validar Biometria
      text: Precisamos validar a sua biometria
      button: VALIDAR
