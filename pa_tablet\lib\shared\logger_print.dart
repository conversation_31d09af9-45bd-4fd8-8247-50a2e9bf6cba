import 'package:flutter/cupertino.dart';
import 'package:logger/logger.dart';
import 'package:pa_virtual/shared/flavor_config.dart';
import 'package:pa_virtual/utils/locator.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class UnimedLogPrinter extends LogPrinter {
  final String? className;
  late RemoteLog remoteLog;

  UnimedLogPrinter(this.className) {
    remoteLog = Locator.instance.get<RemoteLog>();
  }

  @override
  List<String> log(LogEvent event) {
    var color = PrettyPrinter.defaultLevelColors[event.level];
    // var emoji = PrettyPrinter.levelEmojis[event.level];
    const isRelease = bool.fromEnvironment("dart.vm.product");

    if (event.level == Level.error) {
      remoteLog.error(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    } else if (event.level == Level.debug) {
      remoteLog.debug(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    } else if (event.level == Level.warning) {
      remoteLog.warning(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    } else {
      remoteLog.info(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    }
  }

  List<String> _localLog({
    required AnsiColor? color,
    required bool isRelease,
    required LogEvent event,
  }) {
    if (!isRelease) {
      return [
        color!(
          'class: $className - mode: ${FlavorConfig.instance!.name} - msg: ${event.message}',
        ),
      ];
    } else {
      return [];
    }
  }
}

class ConsoleOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      debugPrint(line);
    }
  }
}

class UnimedLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return true;
  }
}

class UnimedLogger extends Logger {
  final dynamic className;

  UnimedLogger({this.className})
      : super(
          printer: UnimedLogPrinter(className),
          filter: UnimedLogFilter(),
        );
}

class BiometryLogger {
  static final logger = UnimedLogger(className: 'BiometryLogger');

  static biometryLogger(String message, String mode) {
    switch (mode) {
      case 'd':
        {
          logger.d(message);
          break;
        }
      case 'e':
        {
          logger.e(message);
          break;
        }
      default:
        {
          logger.i(message);
        }
    }
  }
}
