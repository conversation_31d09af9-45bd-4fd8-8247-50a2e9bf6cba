import 'package:equatable/equatable.dart';
import 'package:pa_tablet/model/unit_model.dart';

abstract class UnityEvent extends Equatable {
  const UnityEvent();

  @override
  List<Object?> get props => [];
}

class GetUnitysEvent extends UnityEvent {
  final String password;

  const GetUnitysEvent({required this.password});
}

class SelectUnityEvent extends UnityEvent {
  final UnitModel unity;

  const SelectUnityEvent({required this.unity});
}

class GetSelectedUnityEvent extends UnityEvent {
  const GetSelectedUnityEvent();
}
