import 'package:pa_virtual/shared/flavor_config.dart' as pavirtualpackage;
import '../flavor_config.dart';

class PAVirtualUtils {
  static pavirtualpackage.DefaultCredencial convertDefaultCredentials(
      DefaultCredencial defaultCredencial) {
    return pavirtualpackage.DefaultCredencial(
        url: defaultCredencial.url,
        user: defaultCredencial.user,
        password: defaultCredencial.password);
  }
}
