// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAX1yuOiuOukZ3RFEDWUNCZ3RgV1gB9o2c',
    appId: '1:1087143606719:android:90e710c3c4064c7f87490a',
    messagingSenderId: '1087143606719',
    projectId: 'pronto-atendimento-tablet',
    storageBucket: 'pronto-atendimento-tablet.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCzr5p0JmAxfcea15uFMYvtJZJbszIHNhs',
    appId: '1:1087143606719:ios:9f2df1ee3260c89b87490a',
    messagingSenderId: '1087143606719',
    projectId: 'pronto-atendimento-tablet',
    storageBucket: 'pronto-atendimento-tablet.appspot.com',
    iosClientId: '1087143606719-mo9s9414188hsj6hlkchjss92k240bbi.apps.googleusercontent.com',
    iosBundleId: 'br.com.unimedfortaleza.pavirtual.tablet',
  );
}
