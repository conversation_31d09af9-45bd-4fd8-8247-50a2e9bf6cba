import 'package:package_info/package_info.dart';

class VersionInfo {
  final String appName;
  final String packageName;
  final String version;
  final String buildNumber;

  VersionInfo({
    required this.appName,
    required this.packageName,
    required this.version,
    required this.buildNumber,
  });
}

class VersionService {
  VersionInfo _info = VersionInfo(
    appName: 'ND',
    packageName: 'ND',
    version: 'ND',
    buildNumber: 'ND',
  );
  // VersionInfo get info => _info;

  Future<VersionInfo> getInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();

    _info = VersionInfo(
      appName: packageInfo.appName,
      packageName: packageInfo.packageName,
      version: packageInfo.version,
      buildNumber: packageInfo.buildNumber,
    );

    return _info;
  }
}
