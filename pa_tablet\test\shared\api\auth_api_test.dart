import 'package:flutter_test/flutter_test.dart';
import 'package:pa_tablet/model/pa_virtual_login.vo.dart';
import 'package:pa_tablet/shared/api/auth_api.dart';
import 'package:pa_tablet/shared/http_client.dart';

void main() {
  final httpClient = UnimedHttpClient();
  final authApi = AuthApi(httpClient);

  //este teste nao vai funcionar ainda, estou vendo como resolver o problema da intancia do locator sendo chamado como null

  group('AuthApi', () {
    test('login should return a valid LoginPAVirtualVO', () async {
      final result = await authApi.login(cpfOrCart: '08488542330');

      expect(result, isA<LoginPAVirtualVO>());
    });
  });
}
