{"roots": ["pa_tablet"], "packages": [{"name": "pa_tablet", "version": "2.0.1+201003", "dependencies": ["after_layout", "auto_size_text", "collection", "cupertino_icons", "equatable", "evaluation", "firebase_core", "firebase_crashlytics", "flutter", "flutter_bloc", "flutter_i18n", "flutter_localizations", "flutter_spinkit", "get_it", "graphql", "header_login", "http", "hydrated_bloc", "intl", "json_annotation", "logger", "mask_text_input_formatter", "native_device_orientation", "pa_virtual", "package_info", "path_provider", "remote_log_elastic", "splash_unimed"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "pa_virtual", "version": "7.1.3", "dependencies": ["after_layout", "archive", "auto_size_text", "biometria_perfilapps", "cached_network_image", "camera", "collection", "equatable", "evaluation", "flutter", "flutter_background_service", "flutter_bloc", "flutter_i18n", "flutter_local_notifications", "graphql", "http_client", "intl", "json_annotation", "mask_text_input_formatter", "native_device_orientation", "numberpicker", "path", "path_provider", "pdfx", "permission_handler", "remote_log_elastic", "share", "shared_preferences", "teleconsulta_unimed", "unimed_select", "url_launcher", "vibration", "wakelock_plus", "websocket_service"]}, {"name": "header_login", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "evaluation", "version": "5.0.0", "dependencies": ["device_info_plus", "flutter", "http", "intl", "package_info"]}, {"name": "splash_unimed", "version": "2.0.1", "dependencies": ["flutter", "flutter_spinkit", "rive"]}, {"name": "remote_log_elastic", "version": "4.0.0", "dependencies": ["connectivity", "device_info_plus", "flutter", "http", "intl", "package_info"]}, {"name": "hydrated_bloc", "version": "7.1.0", "dependencies": ["bloc", "hive", "meta", "synchronized"]}, {"name": "flutter_bloc", "version": "7.3.3", "dependencies": ["bloc", "flutter", "provider"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "auto_size_text", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "package_info", "version": "2.0.2", "dependencies": ["flutter"]}, {"name": "flutter_i18n", "version": "0.36.3", "dependencies": ["flutter", "flutter_localizations", "flutter_web_plugins", "http", "intl", "logging", "path", "rxdart", "toml", "xml2json", "yaml"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "websocket_service", "version": "1.0.0", "dependencies": ["flutter", "socket_io_client"]}, {"name": "url_launcher", "version": "6.3.0", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "unimed_select", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "teleconsulta_unimed", "version": "8.0.0", "dependencies": ["after_layout", "collection", "dart_jsonwebtoken", "events_emitter", "flutter", "flutter_hooks", "flutter_web_plugins", "get_it", "google_fonts", "http", "intl", "logger", "native_device_orientation", "package_info", "permission_handler", "plugin_platform_interface", "remote_log_elastic", "twilio_programmable_video", "uuid", "wakelock_plus"]}, {"name": "share", "version": "2.0.4", "dependencies": ["flutter", "meta", "mime"]}, {"name": "http_client", "version": "2.0.0", "dependencies": ["characters", "flutter", "http", "logger", "package_info_plus"]}, {"name": "flutter_local_notifications", "version": "18.0.1", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "biometria_perfilapps", "version": "6.0.0", "dependencies": ["camera", "equatable", "flutter", "flutter_bloc", "flutter_image_compress", "flutter_spinkit", "get_it", "http", "image", "image_picker", "logger", "package_info_plus", "permission_handler", "remote_log_elastic"]}, {"name": "device_info_plus", "version": "10.1.2", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "rive", "version": "0.11.17", "dependencies": ["collection", "flutter", "flutter_web_plugins", "http", "meta", "plugin_platform_interface", "rive_common"]}, {"name": "connectivity", "version": "3.0.6", "dependencies": ["connectivity_for_web", "connectivity_macos", "connectivity_platform_interface", "flutter", "meta"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "xml2json", "version": "6.2.7", "dependencies": ["xml"]}, {"name": "toml", "version": "0.16.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "url_launcher_ios", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "twilio_programmable_video", "version": "1.1.1", "dependencies": ["collection", "enum_to_string", "flutter", "permission_handler", "twilio_programmable_video_platform_interface", "twilio_programmable_video_web"]}, {"name": "google_fonts", "version": "6.1.0", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "events_emitter", "version": "0.6.0", "dependencies": ["rxdart"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "rive_common", "version": "0.2.7", "dependencies": ["collection", "ffi", "flutter", "flutter_web_plugins", "graphs", "http", "meta", "plugin_platform_interface"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "twilio_programmable_video_web", "version": "1.1.0", "dependencies": ["collection", "dartlin", "enum_to_string", "flutter", "flutter_web_plugins", "js", "twilio_programmable_video_platform_interface", "version"]}, {"name": "twilio_programmable_video_platform_interface", "version": "1.1.0", "dependencies": ["collection", "dartlin", "enum_to_string", "equatable", "flutter", "meta", "plugin_platform_interface"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "numberpicker", "version": "2.1.2", "dependencies": ["flutter", "infinite_listview"]}, {"name": "infinite_listview", "version": "1.1.0", "dependencies": ["flutter"]}, {"name": "camera", "version": "0.10.6", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "bloc", "version": "7.2.1", "dependencies": ["meta"]}, {"name": "socket_io_client", "version": "1.0.2", "dependencies": ["js", "logging", "socket_io_common"]}, {"name": "flutter_local_notifications_platform_interface", "version": "8.0.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "5.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "connectivity_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "connectivity_macos", "version": "0.2.1+2", "dependencies": ["connectivity_platform_interface", "flutter"]}, {"name": "connectivity_for_web", "version": "0.4.0+1", "dependencies": ["connectivity_platform_interface", "flutter", "flutter_web_plugins"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "socket_io_common", "version": "1.0.1", "dependencies": ["logging"]}, {"name": "flutter_spinkit", "version": "5.2.1", "dependencies": ["flutter"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_hooks", "version": "0.20.5", "dependencies": ["flutter"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "dartlin", "version": "0.6.3", "dependencies": []}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "flutter_lints", "version": "2.0.3", "dependencies": ["lints"]}, {"name": "after_layout", "version": "1.2.0", "dependencies": ["flutter"]}, {"name": "pdfx", "version": "2.9.2", "dependencies": ["extension", "flutter", "flutter_web_plugins", "meta", "photo_view", "plugin_platform_interface", "synchronized", "universal_platform", "uuid", "vector_math", "web"]}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "photo_view", "version": "0.15.0", "dependencies": ["flutter"]}, {"name": "extension", "version": "0.6.0", "dependencies": ["meta"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "enum_to_string", "version": "2.2.1", "dependencies": []}, {"name": "version", "version": "2.0.3", "dependencies": []}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "lints", "version": "2.1.1", "dependencies": []}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "wakelock_plus", "version": "1.3.2", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_background_service", "version": "5.1.0", "dependencies": ["flutter", "flutter_background_service_android", "flutter_background_service_ios", "flutter_background_service_platform_interface"]}, {"name": "flutter_background_service_platform_interface", "version": "5.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "flutter_background_service_ios", "version": "5.0.3", "dependencies": ["flutter", "flutter_background_service_platform_interface"]}, {"name": "flutter_background_service_android", "version": "6.3.0", "dependencies": ["flutter", "flutter_background_service_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "win32_registry", "version": "1.1.5", "dependencies": ["ffi", "win32"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "logger", "version": "2.6.0", "dependencies": []}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "mime", "version": "1.0.6", "dependencies": []}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "native_device_orientation", "version": "1.2.1", "dependencies": ["flutter", "meta"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "vibration", "version": "1.9.0", "dependencies": ["flutter", "vibration_platform_interface"]}, {"name": "vibration_platform_interface", "version": "0.0.3", "dependencies": ["device_info_plus", "flutter", "plugin_platform_interface"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "mask_text_input_formatter", "version": "2.9.0", "dependencies": ["flutter"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "flutter_image_compress", "version": "2.4.0", "dependencies": ["flutter", "flutter_image_compress_common", "flutter_image_compress_macos", "flutter_image_compress_ohos", "flutter_image_compress_platform_interface", "flutter_image_compress_web"]}, {"name": "flutter_image_compress_web", "version": "0.1.5", "dependencies": ["flutter", "flutter_image_compress_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_image_compress_platform_interface", "version": "1.0.5", "dependencies": ["cross_file", "flutter", "plugin_platform_interface"]}, {"name": "flutter_image_compress_ohos", "version": "0.0.3", "dependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_macos", "version": "1.0.3", "dependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_common", "version": "1.0.6", "dependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "graphql", "version": "5.2.1", "dependencies": ["collection", "gql", "gql_dedupe_link", "gql_error_link", "gql_exec", "gql_http_link", "gql_link", "gql_transform_link", "hive", "http", "meta", "normalize", "path", "rxdart", "stream_channel", "uuid", "web_socket_channel"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "normalize", "version": "0.9.1", "dependencies": ["collection", "gql"]}, {"name": "get_it", "version": "7.7.0", "dependencies": ["async", "collection", "meta"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "camera_android", "version": "0.10.10+3", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "dart_jsonwebtoken", "version": "2.17.0", "dependencies": ["clock", "collection", "convert", "crypto", "ed25519_edwards", "pointycastle"]}, {"name": "ed25519_edwards", "version": "0.3.1", "dependencies": ["adaptive_number", "collection", "convert", "crypto"]}, {"name": "adaptive_number", "version": "1.0.0", "dependencies": ["fixnum"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "gql_dedupe_link", "version": "2.0.4-alpha+1715521079596", "dependencies": ["async", "gql_exec", "gql_link", "meta"]}, {"name": "gql_exec", "version": "1.1.1-alpha+1699813812660", "dependencies": ["collection", "gql", "meta"]}, {"name": "gql_link", "version": "1.0.1-alpha+1730759315378", "dependencies": ["gql", "gql_exec", "meta"]}, {"name": "gql", "version": "1.0.1-alpha+1730759315362", "dependencies": ["collection", "meta", "source_span", "yaml"]}, {"name": "gql_transform_link", "version": "1.0.0", "dependencies": ["gql_exec", "gql_link"]}, {"name": "gql_error_link", "version": "1.0.0+1", "dependencies": ["async", "gql_exec", "gql_link", "meta"]}, {"name": "gql_http_link", "version": "1.1.0", "dependencies": ["gql", "gql_exec", "gql_link", "http", "http_parser", "meta"]}, {"name": "camera_avfoundation", "version": "0.9.20+1", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "firebase_core", "version": "2.32.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.23.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_crashlytics", "version": "3.5.7", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_crashlytics_platform_interface", "flutter", "stack_trace"]}, {"name": "firebase_crashlytics_platform_interface", "version": "3.6.35", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "_flutterfire_internals", "version": "1.3.35", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}], "configVersion": 1}