import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pa_tablet/model/pa_virtual_login.vo.dart';
import 'package:pa_tablet/shared/api/auth_api.dart';
import 'package:pa_tablet/shared/locator.dart';

import 'package:remote_log_elastic/remote_log_elastic.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthInitial());

  @override
  Stream<AuthState> mapEventToState(
    AuthEvent event,
  ) async* {
    if (event is LoginEvent) {
      try {
        yield AuthLoading();
        final api = Locator.instance.get<AuthApi>();

        Locator.instance.get<RemoteLog>().setCard(event.carteira);

        final loginPAVirtualVO = await api.login(cpfOrCart: event.carteira);

        //Locator.instance.get<RemoteLog>().setUserId(loginPAVirtualVO.cpf);
        yield AuthDone(loginPAVirtualVO: loginPAVirtualVO);
      } catch (e) {
        yield AuthError(message: e.toString());
      }
    } else if (event is VerifyAnswerEvent) {
      if (event.userAnswer == event.correctAnswer) {
        yield AuthAnswerVerified(
            redirectTo: event.redirectTo, cardLogged: event.cardlogged);
      } else {
        yield const AuthError(message: "Resposta inválida");
      }
    }
  }
}
